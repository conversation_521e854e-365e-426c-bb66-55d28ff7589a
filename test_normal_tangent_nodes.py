# اختبار عرض إعدادات عقد Normal و Tangent المتصلة
# قم بتشغيل هذا الكود في Blender لاختبار الميزة الجديدة

import bpy

def test_normal_tangent_connections():
    """اختبار عرض العقد المتصلة بـ Normal و Tangent"""
    
    print("=== اختبار عقد Normal و Tangent ===\n")
    
    obj = bpy.context.object
    if not obj or not obj.active_material:
        print("❌ لا يوجد كائن محدد أو مادة نشطة")
        return
    
    material = obj.active_material
    print(f"📦 Material: {material.name}")
    
    if not material.use_nodes:
        print("⚠️  Material doesn't use nodes")
        return
    
    nodes = material.node_tree.nodes
    
    # البحث عن عقدة Principled BSDF
    principled_nodes = [n for n in nodes if n.type == 'BSDF_PRINCIPLED']
    if not principled_nodes:
        print("❌ لا توجد عقدة Principled BSDF")
        return
    
    principled = principled_nodes[0]
    print(f"✅ Found Principled BSDF: {principled.name}")
    
    # التحقق من اتصالات Normal و Tangent
    special_inputs = ['Normal', 'Tangent']
    
    for input_name in special_inputs:
        if input_name in principled.inputs:
            input_socket = principled.inputs[input_name]
            print(f"\n🔍 Checking {input_name} input:")
            
            if input_socket.is_linked:
                connected_node = input_socket.links[0].from_node
                print(f"  🔗 Connected to: {connected_node.name} (Type: {connected_node.type})")
                
                # عرض مدخلات العقدة المتصلة
                editable_inputs = [inp for inp in connected_node.inputs 
                                 if hasattr(inp, 'default_value') and not inp.is_linked]
                
                if editable_inputs:
                    print(f"  📝 Editable inputs in {connected_node.name}:")
                    for inp in editable_inputs:
                        print(f"    • {inp.name}: {inp.default_value}")
                else:
                    print(f"  🔗 All inputs in {connected_node.name} are connected")
                
                # عرض المدخلات المتصلة أيضاً
                connected_inputs = [inp for inp in connected_node.inputs if inp.is_linked]
                if connected_inputs:
                    print(f"  🔗 Connected inputs in {connected_node.name}:")
                    for inp in connected_inputs:
                        from_node = inp.links[0].from_node
                        print(f"    • {inp.name}: Connected to {from_node.name}")
            else:
                print(f"  ❌ {input_name} is not connected")
                if hasattr(input_socket, 'default_value'):
                    print(f"  📊 Default value: {input_socket.default_value}")
    
    print(f"\n✅ هذا ما ستراه في القائمة المحدثة!")

def create_test_normal_map():
    """إنشاء مثال لعقدة Normal Map للاختبار"""
    
    obj = bpy.context.object
    if not obj or not obj.active_material:
        print("❌ لا يوجد كائن محدد أو مادة نشطة")
        return
    
    material = obj.active_material
    
    if not material.use_nodes:
        material.use_nodes = True
    
    nodes = material.node_tree.nodes
    links = material.node_tree.links
    
    # البحث عن عقدة Principled BSDF
    principled_nodes = [n for n in nodes if n.type == 'BSDF_PRINCIPLED']
    if not principled_nodes:
        print("❌ لا توجد عقدة Principled BSDF")
        return
    
    principled = principled_nodes[0]
    
    # إنشاء عقدة Normal Map إذا لم تكن موجودة
    normal_map_nodes = [n for n in nodes if n.type == 'NORMAL_MAP']
    if not normal_map_nodes:
        # إنشاء عقدة Normal Map جديدة
        normal_map = nodes.new(type='ShaderNodeNormalMap')
        normal_map.location = (principled.location.x - 300, principled.location.y - 200)
        normal_map.name = "Test Normal Map"
        
        # ربط Normal Map بـ Principled BSDF
        links.new(normal_map.outputs['Normal'], principled.inputs['Normal'])
        
        print(f"✅ تم إنشاء عقدة Normal Map وربطها بـ Principled BSDF")
        print(f"📍 الآن يمكنك اختبار عرض إعدادات Normal Map في القائمة")
    else:
        print(f"✅ عقدة Normal Map موجودة مسبقاً: {normal_map_nodes[0].name}")

# تشغيل الاختبار
if __name__ == "__main__":
    print("🔧 اختبار اتصالات Normal و Tangent:")
    test_normal_tangent_connections()
    
    print("\n" + "="*60)
    print("🛠️  إنشاء مثال Normal Map للاختبار:")
    create_test_normal_map()
    
    print("\n" + "="*60)
    print("🔄 اختبار مرة أخرى بعد إنشاء Normal Map:")
    test_normal_tangent_connections()

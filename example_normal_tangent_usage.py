# مثال عملي على استخدام ميزة عرض إعدادات عقد Normal و Tangent
# هذا المثال يوضح كيف تعمل الميزة الجديدة

import bpy

def create_material_with_normal_map():
    """إنشاء مادة مع Normal Map كمثال"""
    
    # إنشاء مادة جديدة
    material = bpy.data.materials.new(name="Test_Normal_Material")
    material.use_nodes = True
    
    nodes = material.node_tree.nodes
    links = material.node_tree.links
    
    # مسح العقد الافتراضية
    nodes.clear()
    
    # إنشاء عقدة Principled BSDF
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    principled.location = (0, 0)
    
    # إنشاء عقدة Material Output
    output = nodes.new(type='ShaderNodeOutputMaterial')
    output.location = (300, 0)
    
    # ربط Principled BSDF بـ Material Output
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # إنشاء عقدة Normal Map
    normal_map = nodes.new(type='ShaderNodeNormalMap')
    normal_map.location = (-300, -200)
    normal_map.inputs['Strength'].default_value = 1.5  # قيمة مخصصة للاختبار
    
    # إنشاء عقدة Image Texture للـ Normal Map
    image_texture = nodes.new(type='ShaderNodeTexImage')
    image_texture.location = (-600, -200)
    
    # ربط العقد
    links.new(image_texture.outputs['Color'], normal_map.inputs['Color'])
    links.new(normal_map.outputs['Normal'], principled.inputs['Normal'])
    
    print("✅ تم إنشاء مادة مع Normal Map")
    print("📍 Normal Map Strength: 1.5")
    print("🔗 Normal Map متصلة بـ Principled BSDF")
    
    return material

def create_material_with_bump():
    """إنشاء مادة مع Bump Node كمثال"""
    
    # إنشاء مادة جديدة
    material = bpy.data.materials.new(name="Test_Bump_Material")
    material.use_nodes = True
    
    nodes = material.node_tree.nodes
    links = material.node_tree.links
    
    # مسح العقد الافتراضية
    nodes.clear()
    
    # إنشاء عقدة Principled BSDF
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    principled.location = (0, 0)
    
    # إنشاء عقدة Material Output
    output = nodes.new(type='ShaderNodeOutputMaterial')
    output.location = (300, 0)
    
    # ربط Principled BSDF بـ Material Output
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # إنشاء عقدة Bump
    bump = nodes.new(type='ShaderNodeBump')
    bump.location = (-300, -200)
    bump.inputs['Strength'].default_value = 0.8  # قيمة مخصصة للاختبار
    bump.inputs['Distance'].default_value = 0.1  # قيمة مخصصة للاختبار
    
    # إنشاء عقدة Noise Texture للـ Bump
    noise_texture = nodes.new(type='ShaderNodeTexNoise')
    noise_texture.location = (-600, -200)
    noise_texture.inputs['Scale'].default_value = 5.0
    
    # ربط العقد
    links.new(noise_texture.outputs['Fac'], bump.inputs['Height'])
    links.new(bump.outputs['Normal'], principled.inputs['Normal'])
    
    print("✅ تم إنشاء مادة مع Bump Node")
    print("📍 Bump Strength: 0.8, Distance: 0.1")
    print("🔗 Bump Node متصلة بـ Principled BSDF")
    
    return material

def demonstrate_feature():
    """عرض توضيحي للميزة الجديدة"""
    
    print("=== عرض توضيحي لميزة Normal/Tangent Nodes ===\n")
    
    # إنشاء أمثلة
    print("🔧 إنشاء أمثلة للاختبار:")
    normal_material = create_material_with_normal_map()
    bump_material = create_material_with_bump()
    
    print(f"\n📦 تم إنشاء المواد:")
    print(f"  • {normal_material.name} - مع Normal Map")
    print(f"  • {bump_material.name} - مع Bump Node")
    
    print(f"\n🎯 كيفية الاختبار:")
    print("1. حدد كائن Mesh في المشهد")
    print("2. اختر إحدى المواد المنشأة كمادة نشطة")
    print("3. افتح قائمة KH-Tools > Basic Material > خصائص المادة النشطة")
    print("4. ستجد:")
    print("   • خصائص Principled BSDF العادية")
    print("   • قسم خاص للـ Normal Node مع إعداداته")
    
    print(f"\n✨ ما ستراه في القائمة:")
    print("بدلاً من 'Normal: Connected'، ستجد:")
    print("📦 Normal Node: Normal Map")
    print("  • Strength: [شريط تمرير]")
    print("  • Space: [قائمة منسدلة]")
    print("  • Color: Connected (إذا كانت متصلة)")
    
    print(f"\nأو إذا كانت Bump Node:")
    print("📦 Normal Node: Bump")
    print("  • Strength: [شريط تمرير]")
    print("  • Distance: [شريط تمرير]")
    print("  • Height: Connected (إذا كانت متصلة)")

def assign_material_to_active_object(material):
    """تعيين مادة للكائن النشط"""
    
    obj = bpy.context.object
    if obj and obj.type == 'MESH':
        # تعيين المادة
        if len(obj.data.materials) == 0:
            obj.data.materials.append(material)
        else:
            obj.data.materials[0] = material
        
        obj.active_material = material
        print(f"✅ تم تعيين المادة {material.name} للكائن {obj.name}")
        return True
    else:
        print("❌ لا يوجد كائن Mesh محدد")
        return False

# تشغيل العرض التوضيحي
if __name__ == "__main__":
    demonstrate_feature()
    
    print(f"\n" + "="*60)
    print("🚀 تطبيق المادة على الكائن النشط:")
    
    # محاولة تطبيق إحدى المواد على الكائن النشط
    if 'Test_Normal_Material' in bpy.data.materials:
        material = bpy.data.materials['Test_Normal_Material']
        if assign_material_to_active_object(material):
            print("🎉 الآن يمكنك اختبار الميزة الجديدة في القائمة!")
    
    print(f"\n📋 تذكير:")
    print("• قم بتشغيل الإضافة المحدثة")
    print("• افتح قائمة KH-Tools > Basic Material")
    print("• ستجد 'خصائص المادة النشطة' مع الميزة الجديدة")

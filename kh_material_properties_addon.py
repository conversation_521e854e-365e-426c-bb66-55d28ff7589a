import bpy
from bpy.types import Panel, Operator
from bpy.utils import register_class, unregister_class

bl_info = {
    "name": "KH - قائمة خصائص المادة النشطة",
    "author": "KH-Tools Extension",
    "version": (1, 0),
    "blender": (4, 0, 0),
    "location": "View3D > UI > KH-Tools > Basic Material",
    "description": "إضافة قائمة فرعية لعرض وتحرير خصائص المادة النشطة للكائن المحدد",
    "category": "Material",
}


class MATERIAL_PT_active_properties_submenu(Panel):
    """قائمة فرعية لعرض خصائص المادة النشطة للكائن المحدد"""
    bl_label = "خصائص المادة النشطة"
    bl_idname = "MATERIAL_PT_active_properties_submenu"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "KH-Tools"
    bl_parent_id = "OBJECT_PT_Basic_materials"  # ربط بقائمة Basic Material
    bl_options = {'DEFAULT_CLOSED'}

    @classmethod
    def poll(cls, context):
        """التحقق من وجود كائن محدد من نوع MESH مع مادة نشطة"""
        obj = context.object
        return (obj is not None and 
                obj.type == 'MESH' and 
                obj.active_material is not None)

    def draw_header(self, context):
        """رسم رأس القائمة"""
        self.layout.label(text="", icon='MATERIAL')

    def draw(self, context):
        """رسم محتوى القائمة"""
        layout = self.layout
        obj = context.object
        material = obj.active_material
        
        # عرض معلومات المادة
        info_box = layout.box()
        info_box.label(text=f"Material: {material.name}", icon='MATERIAL')
        
        if material.use_nodes:
            nodes = material.node_tree.nodes
            
            # البحث عن عقدة Principled BSDF
            principled_node = None
            for node in nodes:
                if node.type == 'BSDF_PRINCIPLED':
                    principled_node = node
                    break
            
            if principled_node:
                # عرض الخصائص الأساسية لـ Principled BSDF
                self.draw_principled_properties(layout, principled_node)
            
            # البحث عن العقد المجمعة
            group_nodes = [node for node in nodes if node.type == 'GROUP']
            if group_nodes:
                self.draw_group_nodes_properties(layout, group_nodes)
            
            # إذا لم توجد عقد مفيدة
            if not principled_node and not group_nodes:
                box = layout.box()
                box.label(text="No controllable nodes found", icon='INFO')

        else:
            # المادة لا تستخدم العقد
            box = layout.box()
            box.label(text="Material doesn't use nodes", icon='INFO')
            row = box.row()
            row.operator("material.enable_nodes", text="Enable Nodes", icon='NODETREE')

    def draw_principled_properties(self, layout, principled_node):
        """رسم خصائص عقدة Principled BSDF"""
        box = layout.box()
        box.label(text="Principled BSDF", icon='MATERIAL_DATA')

        # عرض جميع المدخلات المتاحة كما هي في المادة
        for input_socket in principled_node.inputs:
            if hasattr(input_socket, 'default_value'):
                row = box.row()

                if input_socket.is_linked:
                    # التحقق من العقد الخاصة (Normal و Tangent)
                    if input_socket.name in ['Normal', 'Tangent']:
                        # عرض إعدادات العقدة المتصلة
                        connected_node = input_socket.links[0].from_node
                        self.draw_connected_node_properties(layout, connected_node, input_socket.name)
                    else:
                        # إذا كان المدخل متصل بعقدة أخرى
                        row.label(text=f"{input_socket.name}: Connected", icon='LINKED')
                else:
                    # إذا كان المدخل غير متصل، اعرض التحكم باسمه الأصلي
                    row.prop(input_socket, "default_value", text=input_socket.name)

    def draw_connected_node_properties(self, layout, connected_node, socket_name):
        """رسم خصائص العقدة المتصلة بـ Normal أو Tangent"""
        box = layout.box()
        box.label(text=f"{socket_name} Node: {connected_node.name}", icon='NODE')

        # عرض مدخلات العقدة المتصلة القابلة للتعديل
        editable_inputs = []
        for inp in connected_node.inputs:
            if hasattr(inp, 'default_value') and not inp.is_linked:
                editable_inputs.append(inp)

        if editable_inputs:
            for input_socket in editable_inputs:
                row = box.row()
                row.prop(input_socket, "default_value", text=input_socket.name)
        else:
            row = box.row()
            row.label(text="All inputs connected", icon='LINKED')

    def draw_group_nodes_properties(self, layout, group_nodes):
        """رسم خصائص العقد المجمعة"""
        for group_node in group_nodes:
            box = layout.box()
            box.label(text=f"Group Node: {group_node.name}", icon='NODETREE')

            # عرض مدخلات العقدة المجمعة القابلة للتعديل
            editable_inputs = [inp for inp in group_node.inputs
                             if not inp.is_linked and hasattr(inp, 'default_value')]

            if editable_inputs:
                for input_socket in editable_inputs:
                    row = box.row()
                    # عرض الاسم الأصلي للمدخل كما هو في المادة
                    row.prop(input_socket, "default_value", text=input_socket.name)
            else:
                row = box.row()
                row.label(text="All inputs connected", icon='LINKED')




class MATERIAL_OT_enable_nodes(Operator):
    """Enable nodes in active material"""
    bl_idname = "material.enable_nodes"
    bl_label = "Enable Nodes"
    bl_description = "Enable node system in active material"

    def execute(self, context):
        obj = context.object
        if obj and obj.active_material:
            obj.active_material.use_nodes = True
            self.report({'INFO'}, "Nodes enabled in material")
        else:
            self.report({'ERROR'}, "No active material")
        return {'FINISHED'}


# قائمة الفئات للتسجيل
classes = [
    MATERIAL_PT_active_properties_submenu,
    MATERIAL_OT_enable_nodes,
]


def register():
    """تسجيل الفئات"""
    for cls in classes:
        register_class(cls)


def unregister():
    """إلغاء تسجيل الفئات"""
    for cls in reversed(classes):
        unregister_class(cls)


if __name__ == "__main__":
    register()
